// Quick test to verify employee types API
// Run this in browser console after logging in

async function testEmployeeTypesAPI() {
  try {
    // Get the auth data from localStorage
    const authData = localStorage.getItem('kazisync_auth');
    if (!authData) {
      console.error('No auth data found. Please login first.');
      return;
    }

    const parsedAuth = JSON.parse(authData);
    const token = parsedAuth.access_token;

    if (!token) {
      console.error('No access token found. Please login first.');
      return;
    }

    console.log('🔍 Testing employee types API...');
    console.log('Token:', token.substring(0, 20) + '...');

    // Check company data
    let countryCode = null;
    if (parsedAuth.companies && parsedAuth.companies.length > 0) {
      const company = parsedAuth.companies[0];
      console.log('🏢 Company:', company.company_name);

      if (company.company_dict?.country_code) {
        countryCode = company.company_dict.country_code.toLowerCase();
        console.log('🌍 Country code:', countryCode);
      }
    }

    if (!countryCode) {
      console.error('❌ No country code found in company data');
      return;
    }

    // Test the API endpoint directly
    const apiUrl = `https://sms.remmittance.com/api/countries/${countryCode}/employee-types`;
    console.log('📡 API URL:', apiUrl);

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ API Response:', data);
    console.log('📈 Employee types count:', data.employee_types?.length || 0);

    if (data.employee_types && data.employee_types.length > 0) {
      console.log('👥 Employee types found:');
      data.employee_types.forEach((type, index) => {
        console.log(`  ${index + 1}. ${type.name} (${type.code}) - ID: ${type.employee_type_id}`);
      });
    } else {
      console.log('⚠️ No employee types found in response');
    }

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Run the test
console.log('🚀 Starting employee types API test...');
testEmployeeTypesAPI();
