// Quick test to verify employee types API and localStorage
// Run this in browser console after logging in

function debugLocalStorage() {
  console.log('🔍 Debugging localStorage...');

  // Check what's in localStorage
  const authData = localStorage.getItem('kazisync_auth');
  if (!authData) {
    console.error('❌ No kazisync_auth found in localStorage');
    console.log('Available localStorage keys:', Object.keys(localStorage));
    return null;
  }

  try {
    const parsedAuth = JSON.parse(authData);
    console.log('✅ Auth data found:', parsedAuth);

    // Check companies structure
    if (parsedAuth.companies) {
      console.log('🏢 Companies array:', parsedAuth.companies);
      if (parsedAuth.companies.length > 0) {
        const company = parsedAuth.companies[0];
        console.log('📋 First company:', company);

        if (company.company_dict) {
          console.log('📊 Company dict:', company.company_dict);
          console.log('🌍 Country code:', company.company_dict.country_code);
        }
      }
    } else {
      console.log('⚠️ No companies array found');
    }

    return parsedAuth;
  } catch (error) {
    console.error('💥 Error parsing auth data:', error);
    return null;
  }
}

async function testEmployeeTypesAPI() {
  try {
    const parsedAuth = debugLocalStorage();
    if (!parsedAuth) return;

    const token = parsedAuth.access_token;
    if (!token) {
      console.error('❌ No access token found');
      return;
    }

    console.log('🔑 Token found:', token.substring(0, 20) + '...');

    // Check company data
    let countryCode = null;
    if (parsedAuth.companies && parsedAuth.companies.length > 0) {
      const company = parsedAuth.companies[0];
      console.log('🏢 Company:', company.company_name);

      if (company.company_dict?.country_code) {
        countryCode = company.company_dict.country_code.toLowerCase();
        console.log('🌍 Country code (lowercase):', countryCode);
        console.log('🌍 Original country code:', company.company_dict.country_code);
      } else {
        console.error('❌ No country_code in company_dict');
      }
    } else {
      console.error('❌ No companies found');
      return;
    }

    if (!countryCode) {
      console.error('❌ No country code found in company data');
      return;
    }

    // Test the API endpoint directly
    const apiUrl = `https://sms.remmittance.com/api/countries/${countryCode}/employee-types`;
    console.log('📡 API URL:', apiUrl);

    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });

    console.log('📊 Response status:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('❌ API Error:', errorText);
      return;
    }

    const data = await response.json();
    console.log('✅ API Response:', data);
    console.log('📈 Employee types count:', data.employee_types?.length || 0);

    if (data.employee_types && data.employee_types.length > 0) {
      console.log('👥 Employee types found:');
      data.employee_types.forEach((type, index) => {
        console.log(`  ${index + 1}. ${type.name} (${type.code}) - ID: ${type.employee_type_id}`);
      });
    } else {
      console.log('⚠️ No employee types found in response');
    }

  } catch (error) {
    console.error('💥 Test failed:', error);
  }
}

// Run the test
console.log('🚀 Starting localStorage and employee types API test...');
testEmployeeTypesAPI();
