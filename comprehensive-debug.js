// Comprehensive debugging for employee types issue
// Run this in browser console after logging in

console.log('🚀 Starting comprehensive debug...');

// Step 1: Check localStorage
function step1_checkLocalStorage() {
  console.log('\n📦 STEP 1: Checking localStorage');
  
  const authData = localStorage.getItem('kazisync_auth');
  if (!authData) {
    console.error('❌ No kazisync_auth found in localStorage');
    console.log('Available keys:', Object.keys(localStorage));
    return null;
  }
  
  console.log('✅ kazisync_auth found, length:', authData.length);
  
  try {
    const parsed = JSON.parse(authData);
    console.log('✅ Successfully parsed auth data');
    console.log('Keys in auth data:', Object.keys(parsed));
    return parsed;
  } catch (error) {
    console.error('❌ Failed to parse auth data:', error);
    return null;
  }
}

// Step 2: Check companies structure
function step2_checkCompanies(authData) {
  console.log('\n🏢 STEP 2: Checking companies structure');
  
  if (!authData) {
    console.error('❌ No auth data provided');
    return null;
  }
  
  if (authData.companies) {
    console.log('✅ Companies array found, length:', authData.companies.length);
    if (authData.companies.length > 0) {
      const company = authData.companies[0];
      console.log('✅ First company:', {
        company_id: company.company_id,
        company_name: company.company_name,
        database_name: company.database_name
      });
      return company;
    } else {
      console.error('❌ Companies array is empty');
      return null;
    }
  } else {
    console.error('❌ No companies array found');
    console.log('Available keys:', Object.keys(authData));
    return null;
  }
}

// Step 3: Check company_dict structure
function step3_checkCompanyDict(company) {
  console.log('\n📊 STEP 3: Checking company_dict structure');
  
  if (!company) {
    console.error('❌ No company provided');
    return null;
  }
  
  if (company.company_dict) {
    console.log('✅ company_dict found');
    console.log('Keys in company_dict:', Object.keys(company.company_dict));
    
    const dict = company.company_dict;
    console.log('Company dict details:', {
      company_id: dict.company_id,
      company_name: dict.company_name,
      country_code: dict.country_code,
      country_currency: dict.country_currency,
      country_name: dict.country_name
    });
    
    return dict;
  } else {
    console.error('❌ No company_dict found');
    console.log('Available keys in company:', Object.keys(company));
    return null;
  }
}

// Step 4: Test country code extraction
function step4_testCountryCode(companyDict) {
  console.log('\n🌍 STEP 4: Testing country code extraction');
  
  if (!companyDict) {
    console.error('❌ No company dict provided');
    return null;
  }
  
  if (companyDict.country_code) {
    console.log('✅ Country code found:', companyDict.country_code);
    console.log('✅ Lowercase version:', companyDict.country_code.toLowerCase());
    return companyDict.country_code.toLowerCase();
  } else {
    console.error('❌ No country_code in company_dict');
    return null;
  }
}

// Step 5: Test API URL construction
function step5_testAPIUrl(countryCode) {
  console.log('\n📡 STEP 5: Testing API URL construction');
  
  if (!countryCode) {
    console.error('❌ No country code provided');
    return null;
  }
  
  const apiUrl = `https://sms.remmittance.com/api/countries/${countryCode}/employee-types`;
  console.log('✅ API URL:', apiUrl);
  return apiUrl;
}

// Step 6: Test API call
async function step6_testAPICall(apiUrl, token) {
  console.log('\n🔗 STEP 6: Testing API call');
  
  if (!apiUrl || !token) {
    console.error('❌ Missing API URL or token');
    return;
  }
  
  try {
    console.log('Making request to:', apiUrl);
    const response = await fetch(apiUrl, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json'
      }
    });
    
    console.log('Response status:', response.status);
    
    if (response.ok) {
      const data = await response.json();
      console.log('✅ API call successful');
      console.log('Employee types count:', data.employee_types?.length || 0);
      
      if (data.employee_types && data.employee_types.length > 0) {
        console.log('Employee types:');
        data.employee_types.forEach((type, index) => {
          console.log(`  ${index + 1}. ${type.name} (${type.code})`);
        });
      }
    } else {
      const errorText = await response.text();
      console.error('❌ API call failed:', errorText);
    }
  } catch (error) {
    console.error('❌ API call error:', error);
  }
}

// Run all steps
async function runFullDebug() {
  const authData = step1_checkLocalStorage();
  if (!authData) return;
  
  const company = step2_checkCompanies(authData);
  if (!company) return;
  
  const companyDict = step3_checkCompanyDict(company);
  if (!companyDict) return;
  
  const countryCode = step4_testCountryCode(companyDict);
  if (!countryCode) return;
  
  const apiUrl = step5_testAPIUrl(countryCode);
  if (!apiUrl) return;
  
  const token = authData.access_token;
  if (!token) {
    console.error('❌ No access token found');
    return;
  }
  
  await step6_testAPICall(apiUrl, token);
  
  console.log('\n🎉 Debug complete!');
}

// Run the debug
runFullDebug();
