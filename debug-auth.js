// Debug authentication and localStorage
// Run this in browser console

function debugAuth() {
  console.log('🔍 Debugging authentication...');
  
  // Check localStorage
  console.log('📦 localStorage contents:');
  for (let i = 0; i < localStorage.length; i++) {
    const key = localStorage.key(i);
    const value = localStorage.getItem(key);
    console.log(`  ${key}:`, value?.substring(0, 100) + (value?.length > 100 ? '...' : ''));
  }
  
  // Check kazisync_auth specifically
  const authData = localStorage.getItem('kazisync_auth');
  if (authData) {
    try {
      const parsed = JSON.parse(authData);
      console.log('✅ kazisync_auth parsed:', parsed);
      
      if (parsed.companies) {
        console.log('🏢 Companies found:', parsed.companies.length);
        parsed.companies.forEach((company, index) => {
          console.log(`  Company ${index + 1}:`, company.company_name);
          if (company.company_dict) {
            console.log(`    Country code: ${company.company_dict.country_code}`);
            console.log(`    Currency: ${company.company_dict.country_currency}`);
          }
        });
      } else {
        console.log('❌ No companies array found');
      }
    } catch (error) {
      console.error('💥 Error parsing kazisync_auth:', error);
    }
  } else {
    console.log('❌ No kazisync_auth found in localStorage');
  }
}

function clearAuth() {
  console.log('🧹 Clearing authentication data...');
  localStorage.removeItem('kazisync_auth');
  console.log('✅ Auth data cleared. Please login again.');
}

function testCountryCodeExtraction() {
  console.log('🌍 Testing country code extraction...');
  
  const authData = localStorage.getItem('kazisync_auth');
  if (!authData) {
    console.error('❌ No auth data found');
    return;
  }
  
  try {
    const parsed = JSON.parse(authData);
    
    if (parsed.companies && parsed.companies.length > 0) {
      const company = parsed.companies[0];
      console.log('🏢 First company:', company);
      
      if (company.company_dict?.country_code) {
        const countryCode = company.company_dict.country_code;
        console.log('🌍 Country code found:', countryCode);
        console.log('🌍 Lowercase:', countryCode.toLowerCase());
        
        // Test the API URL construction
        const apiUrl = `https://sms.remmittance.com/api/countries/${countryCode.toLowerCase()}/employee-types`;
        console.log('📡 API URL would be:', apiUrl);
      } else {
        console.error('❌ No country_code in company_dict');
        console.log('Available company_dict keys:', Object.keys(company.company_dict || {}));
      }
    } else {
      console.error('❌ No companies found');
    }
  } catch (error) {
    console.error('💥 Error:', error);
  }
}

// Run debug
debugAuth();
console.log('\n🔧 Available functions:');
console.log('- debugAuth() - Show auth data');
console.log('- clearAuth() - Clear auth data');
console.log('- testCountryCodeExtraction() - Test country code extraction');
