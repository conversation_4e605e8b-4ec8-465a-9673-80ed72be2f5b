'use client';

import React, { useState } from 'react';
import { getEmployeeTypesForCompany } from '@/lib/employee';
import { getCompanyCountryCode, getAccessToken } from '@/lib/auth';

const EmployeeTypesDebug: React.FC = () => {
  const [result, setResult] = useState<string>('');
  const [loading, setLoading] = useState(false);

  const testEmployeeTypes = async () => {
    setLoading(true);
    setResult('Testing...\n');

    try {
      // Test 1: Check if we have a token
      const token = getAccessToken();
      setResult(prev => prev + `Token available: ${token ? 'Yes' : 'No'}\n`);
      if (token) {
        setResult(prev => prev + `Token preview: ${token.substring(0, 20)}...\n`);
      }

      // Test 2: Check country code
      const countryCode = getCompanyCountryCode();
      setResult(prev => prev + `Country code: ${countryCode || 'Not found'}\n`);

      // Test 3: Try to fetch employee types
      if (token && countryCode) {
        setResult(prev => prev + 'Fetching employee types...\n');
        const employeeTypes = await getEmployeeTypesForCompany();
        setResult(prev => prev + `Employee types count: ${employeeTypes.length}\n`);
        
        if (employeeTypes.length > 0) {
          setResult(prev => prev + 'Employee types:\n');
          employeeTypes.forEach((type, index) => {
            setResult(prev => prev + `  ${index + 1}. ${type.name} (${type.code})\n`);
          });
        }
      } else {
        setResult(prev => prev + 'Cannot fetch employee types - missing token or country code\n');
      }

    } catch (error) {
      setResult(prev => prev + `Error: ${error instanceof Error ? error.message : 'Unknown error'}\n`);
      console.error('Debug test error:', error);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-4 border rounded-lg bg-gray-50">
      <h3 className="text-lg font-semibold mb-4">Employee Types Debug</h3>
      
      <button
        onClick={testEmployeeTypes}
        disabled={loading}
        className="px-4 py-2 bg-blue-500 text-white rounded hover:bg-blue-600 disabled:opacity-50"
      >
        {loading ? 'Testing...' : 'Test Employee Types'}
      </button>

      {result && (
        <div className="mt-4">
          <h4 className="font-medium mb-2">Results:</h4>
          <pre className="bg-white p-3 border rounded text-sm whitespace-pre-wrap">
            {result}
          </pre>
        </div>
      )}
    </div>
  );
};

export default EmployeeTypesDebug;
